[package]
edition = "2021"
name = "embassy-stm32f4-examples"
version = "0.1.0"
license = "MIT OR Apache-2.0"

[dependencies]
# Change stm32f429zi to your chip name, if necessary.
embassy-stm32 = { version = "0.2.0", path = "../../embassy-stm32", features = [
    "defmt",
    "stm32f407ve",
    "unstable-pac",
    "memory-x",
    "time-driver-tim4",
    "exti",
    "chrono",
] }
embassy-sync = { version = "0.7.0", path = "../../embassy-sync", features = [
    "defmt",
] }
embassy-executor = { version = "0.8.0", path = "../../embassy-executor", features = [
    "arch-cortex-m",
    "executor-thread",
    "executor-interrupt",
    "defmt",
] }
embassy-time = { version = "0.4.0", path = "../../embassy-time", features = [
    "defmt",
    "defmt-timestamp-uptime",
    "tick-hz-32_768",
] }
embassy-usb = { version = "0.5.0", path = "../../embassy-usb", features = [
    "defmt",
] }
embassy-net = { version = "0.7.0", path = "../../embassy-net", features = [
    "defmt",
    "tcp",
    "dhcpv4",
    "medium-ethernet",
] }
embassy-net-wiznet = { version = "0.2.0", path = "../../embassy-net-wiznet", features = [
    "defmt",
] }
embassy-futures = { version = "0.1.0", path = "../../embassy-futures" }

defmt = "1.0.1"
defmt-rtt = "1.0.0"

cortex-m = { version = "0.7.6", features = [
    "inline-asm",
    "critical-section-single-core",
] }
cortex-m-rt = "0.7.0"
embedded-hal = "0.2.6"
embedded-hal-bus = { version = "0.2", features = ["async"] }
embedded-io = { version = "0.6.0" }
embedded-io-async = { version = "0.6.1" }
panic-probe = { version = "1.0.0", features = ["print-defmt"] }
futures-util = { version = "0.3.30", default-features = false }
heapless = { version = "0.8", default-features = false }
critical-section = "1.1"
nb = "1.0.0"
embedded-storage = "0.3.1"
micromath = "2.0.0"
usbd-hid = "0.8.1"
static_cell = "2"
chrono = { version = "^0.4", default-features = false }

[[bin]]
name = "aaa"
path = "src/bin/aaa.rs"
test = false
bench = false

[lib]
test = false
bench = false

[profile.release]
debug = 2
