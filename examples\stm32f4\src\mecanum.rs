//! 麦克纳姆轮驱动系统 - 使用四个独立的 DRV8833Single
//!
//! 此模块提供了控制四轮麦克纳姆驱动系统的高级接口。
//! 使用四个独立的 Drv8833Single 实例实现全向运动。
//!
//! # 硬件架构
//!
//! ```text
//! 每个电机使用一个 Drv8833Single:
//! FL Motor ← Drv8833Single (2 PWM pins)
//! FR Motor ← Drv8833Single (2 PWM pins)
//! BL Motor ← Drv8833Single (2 PWM pins)
//! BR Motor ← Drv8833Single (2 PWM pins)
//! 总计: 8 个 PWM 引脚
//! ```

use crate::drv8833::{Direction, Drv8833Single, MotorDriver};
use embassy_stm32::timer::GeneralInstance4Channel;

/// 麦克纳姆驱动的运动方向
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MecanumDirection {
    /// 前进
    Forward,
    /// 后退
    Backward,
    /// 左平移
    Left,
    /// 右平移
    Right,
    /// 顺时针旋转
    RotateClockwise,
    /// 逆时针旋转
    RotateCounterClockwise,
    /// 停止所有电机
    Stop,
}

/// 麦克纳姆驱动操作中可能发生的错误
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum MecanumError {
    /// 电机控制错误
    MotorError,
    /// 无效的速度值 (必须在 -100 到 100 之间)
    InvalidSpeed,
}

/// 使用四个独立 Drv8833Single 的麦克纳姆驱动系统
///
/// 此结构体使用四个独立的 `Drv8833Single` 实例来控制4个麦克纳姆轮。
/// 每个电机都有独立的控制，提供最大的灵活性。
///
/// # 硬件需求
/// - 4 个 Drv8833Single 实例
/// - 8 个 PWM 引脚总计 (每个电机 2 个)
pub struct MecanumDrive<
    TIM1: GeneralInstance4Channel,
    TIM2: GeneralInstance4Channel,
    TIM3: GeneralInstance4Channel,
    TIM4: GeneralInstance4Channel,
> {
    /// 左前轮电机
    fl_motor: Drv8833Single<TIM1>,
    /// 右前轮电机
    fr_motor: Drv8833Single<TIM2>,
    /// 左后轮电机
    bl_motor: Drv8833Single<TIM3>,
    /// 右后轮电机
    br_motor: Drv8833Single<TIM4>,
}

impl<
        TIM1: GeneralInstance4Channel,
        TIM2: GeneralInstance4Channel,
        TIM3: GeneralInstance4Channel,
        TIM4: GeneralInstance4Channel,
    > MecanumDrive<TIM1, TIM2, TIM3, TIM4>
{
    /// 创建新的麦克纳姆驱动系统
    ///
    /// # 参数
    /// * `fl_motor` - 左前轮电机驱动器
    /// * `fr_motor` - 右前轮电机驱动器
    /// * `bl_motor` - 左后轮电机驱动器
    /// * `br_motor` - 右后轮电机驱动器
    pub fn new(
        fl_motor: Drv8833Single<TIM1>,
        fr_motor: Drv8833Single<TIM2>,
        bl_motor: Drv8833Single<TIM3>,
        br_motor: Drv8833Single<TIM4>,
    ) -> Self {
        Self {
            fl_motor,
            fr_motor,
            bl_motor,
            br_motor,
        }
    }

    /// 按特定方向移动机器人
    ///
    /// # 参数
    /// * `direction` - 移动方向
    /// * `speed` - 速度百分比 (0-100)
    pub fn move_direction(&mut self, direction: MecanumDirection, speed: u8) -> Result<(), MecanumError> {
        match direction {
            MecanumDirection::Forward => self.move_forward(speed),
            MecanumDirection::Backward => self.move_backward(speed),
            MecanumDirection::Left => self.strafe_left(speed),
            MecanumDirection::Right => self.strafe_right(speed),
            MecanumDirection::RotateClockwise => self.rotate_clockwise(speed),
            MecanumDirection::RotateCounterClockwise => self.rotate_counter_clockwise(speed),
            MecanumDirection::Stop => self.stop_all(),
        }
    }

    /// 前进 - 所有轮子向前旋转
    pub fn move_forward(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Forward, speed);
        let _ = self.fr_motor.set_motor(Direction::Forward, speed);
        let _ = self.bl_motor.set_motor(Direction::Forward, speed);
        let _ = self.br_motor.set_motor(Direction::Forward, speed);
        Ok(())
    }

    /// 后退 - 所有轮子向后旋转
    pub fn move_backward(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Backward, speed);
        let _ = self.fr_motor.set_motor(Direction::Backward, speed);
        let _ = self.bl_motor.set_motor(Direction::Backward, speed);
        let _ = self.br_motor.set_motor(Direction::Backward, speed);
        Ok(())
    }

    /// 左平移 - 麦克纳姆轮产生侧向运动
    pub fn strafe_left(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Backward, speed);
        let _ = self.fr_motor.set_motor(Direction::Forward, speed);
        let _ = self.bl_motor.set_motor(Direction::Forward, speed);
        let _ = self.br_motor.set_motor(Direction::Backward, speed);
        Ok(())
    }

    /// 右平移 - 麦克纳姆轮产生侧向运动
    pub fn strafe_right(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Forward, speed);
        let _ = self.fr_motor.set_motor(Direction::Backward, speed);
        let _ = self.bl_motor.set_motor(Direction::Backward, speed);
        let _ = self.br_motor.set_motor(Direction::Forward, speed);
        Ok(())
    }

    /// 顺时针旋转 - 左轮前进，右轮后退
    pub fn rotate_clockwise(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Forward, speed);
        let _ = self.fr_motor.set_motor(Direction::Backward, speed);
        let _ = self.bl_motor.set_motor(Direction::Forward, speed);
        let _ = self.br_motor.set_motor(Direction::Backward, speed);
        Ok(())
    }

    /// 逆时针旋转 - 左轮后退，右轮前进
    pub fn rotate_counter_clockwise(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Backward, speed);
        let _ = self.fr_motor.set_motor(Direction::Forward, speed);
        let _ = self.bl_motor.set_motor(Direction::Backward, speed);
        let _ = self.br_motor.set_motor(Direction::Forward, speed);
        Ok(())
    }

    /// 停止所有电机
    pub fn stop_all(&mut self) -> Result<(), MecanumError> {
        let _ = self.fl_motor.coast();
        let _ = self.fr_motor.coast();
        let _ = self.bl_motor.coast();
        let _ = self.br_motor.coast();
        Ok(())
    }

    /// 根据 yaw 偏差纠正角度 (用于 MPU 集成)
    ///
    /// 这个函数根据 yaw 偏差旋转机器人以纠正偏离目标航向的角度。
    /// 正的 yaw_deviation 表示机器人相对于目标顺时针偏转。
    ///
    /// # 参数
    /// * `yaw_deviation` - yaw 偏差角度 (-180 到 180)
    /// * `speed` - 旋转速度 (0-100)
    ///
    /// # 返回
    /// * `bool` - 如果机器人在可接受偏差范围内则返回 true (< 2 度)
    pub fn correct_yaw_deviation(&mut self, yaw_deviation: f32, speed: u8) -> bool {
        // 检查是否在可接受偏差范围内
        if yaw_deviation.abs() < 2.0 {
            let _ = self.stop_all();
            return true;
        }

        // 旋转以纠正偏差
        // 正偏差意味着我们需要逆时针旋转来纠正
        if yaw_deviation > 0.0 {
            let _ = self.rotate_counter_clockwise(speed);
        } else {
            let _ = self.rotate_clockwise(speed);
        }

        false // 尚未纠正
    }

    /// 获取左前轮电机的可变引用
    pub fn fl_motor(&mut self) -> &mut Drv8833Single<TIM1> {
        &mut self.fl_motor
    }

    /// 获取右前轮电机的可变引用
    pub fn fr_motor(&mut self) -> &mut Drv8833Single<TIM2> {
        &mut self.fr_motor
    }

    /// 获取左后轮电机的可变引用
    pub fn bl_motor(&mut self) -> &mut Drv8833Single<TIM3> {
        &mut self.bl_motor
    }

    /// 获取右后轮电机的可变引用
    pub fn br_motor(&mut self) -> &mut Drv8833Single<TIM4> {
        &mut self.br_motor
    }
}

/// 通用麦克纳姆驱动系统 - 支持任何电机驱动器
///
/// 这个结构体可以使用任何实现了 `MotorDriver` trait 的电机驱动器，
/// 包括 DRV8833Single, AT8236Single 等。
///
/// # 用法示例
///
/// ```rust,no_run
/// // 使用 DRV8833
/// let fl_motor = Drv8833Single::new(fl_pwm_in1, fl_pwm_in2);
/// let mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);
///
/// // 使用 AT8236
/// let fl_motor = At8236Single::new(fl_pwm_in1, fl_pwm_in2);
/// let mecanum = GenericMecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);
/// ```
pub struct GenericMecanumDrive<FL, FR, BL, BR>
where
    FL: MotorDriver,
    FR: MotorDriver,
    BL: MotorDriver,
    BR: MotorDriver,
{
    /// 左前轮电机
    fl_motor: FL,
    /// 右前轮电机
    fr_motor: FR,
    /// 左后轮电机
    bl_motor: BL,
    /// 右后轮电机
    br_motor: BR,
}

impl<FL, FR, BL, BR> GenericMecanumDrive<FL, FR, BL, BR>
where
    FL: MotorDriver,
    FR: MotorDriver,
    BL: MotorDriver,
    BR: MotorDriver,
{
    /// 创建新的通用麦克纳姆驱动系统
    ///
    /// # 参数
    /// * `fl_motor` - 左前轮电机驱动器
    /// * `fr_motor` - 右前轮电机驱动器
    /// * `bl_motor` - 左后轮电机驱动器
    /// * `br_motor` - 右后轮电机驱动器
    pub fn new(fl_motor: FL, fr_motor: FR, bl_motor: BL, br_motor: BR) -> Self {
        Self {
            fl_motor,
            fr_motor,
            bl_motor,
            br_motor,
        }
    }

    /// 按特定方向移动机器人
    ///
    /// # 参数
    /// * `direction` - 移动方向
    /// * `speed` - 速度百分比 (0-100)
    pub fn move_direction(&mut self, direction: MecanumDirection, speed: u8) -> Result<(), MecanumError> {
        match direction {
            MecanumDirection::Forward => self.move_forward(speed),
            MecanumDirection::Backward => self.move_backward(speed),
            MecanumDirection::Left => self.strafe_left(speed),
            MecanumDirection::Right => self.strafe_right(speed),
            MecanumDirection::RotateClockwise => self.rotate_clockwise(speed),
            MecanumDirection::RotateCounterClockwise => self.rotate_counter_clockwise(speed),
            MecanumDirection::Stop => self.stop_all(),
        }
    }

    /// 前进 - 所有轮子向前旋转
    pub fn move_forward(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Forward, speed);
        let _ = self.fr_motor.set_motor(Direction::Forward, speed);
        let _ = self.bl_motor.set_motor(Direction::Forward, speed);
        let _ = self.br_motor.set_motor(Direction::Forward, speed);
        Ok(())
    }

    /// 后退 - 所有轮子向后旋转
    pub fn move_backward(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Backward, speed);
        let _ = self.fr_motor.set_motor(Direction::Backward, speed);
        let _ = self.bl_motor.set_motor(Direction::Backward, speed);
        let _ = self.br_motor.set_motor(Direction::Backward, speed);
        Ok(())
    }

    /// 左平移 - 麦克纳姆轮产生侧向运动
    pub fn strafe_left(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Backward, speed);
        let _ = self.fr_motor.set_motor(Direction::Forward, speed);
        let _ = self.bl_motor.set_motor(Direction::Forward, speed);
        let _ = self.br_motor.set_motor(Direction::Backward, speed);
        Ok(())
    }

    /// 右平移 - 麦克纳姆轮产生侧向运动
    pub fn strafe_right(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Forward, speed);
        let _ = self.fr_motor.set_motor(Direction::Backward, speed);
        let _ = self.bl_motor.set_motor(Direction::Backward, speed);
        let _ = self.br_motor.set_motor(Direction::Forward, speed);
        Ok(())
    }

    /// 顺时针旋转 - 左轮前进，右轮后退
    pub fn rotate_clockwise(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Forward, speed);
        let _ = self.fr_motor.set_motor(Direction::Backward, speed);
        let _ = self.bl_motor.set_motor(Direction::Forward, speed);
        let _ = self.br_motor.set_motor(Direction::Backward, speed);
        Ok(())
    }

    /// 逆时针旋转 - 左轮后退，右轮前进
    pub fn rotate_counter_clockwise(&mut self, speed: u8) -> Result<(), MecanumError> {
        let _ = self.fl_motor.set_motor(Direction::Backward, speed);
        let _ = self.fr_motor.set_motor(Direction::Forward, speed);
        let _ = self.bl_motor.set_motor(Direction::Backward, speed);
        let _ = self.br_motor.set_motor(Direction::Forward, speed);
        Ok(())
    }

    /// 停止所有电机
    pub fn stop_all(&mut self) -> Result<(), MecanumError> {
        let _ = self.fl_motor.coast();
        let _ = self.fr_motor.coast();
        let _ = self.bl_motor.coast();
        let _ = self.br_motor.coast();
        Ok(())
    }

    /// 根据 yaw 偏差纠正角度 (用于 MPU 集成)
    ///
    /// 这个函数根据 yaw 偏差旋转机器人以纠正偏离目标航向的角度。
    /// 正的 yaw_deviation 表示机器人相对于目标顺时针偏转。
    ///
    /// # 参数
    /// * `yaw_deviation` - yaw 偏差角度 (-180 到 180)
    /// * `speed` - 旋转速度 (0-100)
    ///
    /// # 返回
    /// * `bool` - 如果机器人在可接受偏差范围内则返回 true (< 2 度)
    pub fn correct_yaw_deviation(&mut self, yaw_deviation: f32, speed: u8) -> bool {
        // 检查是否在可接受偏差范围内
        if yaw_deviation.abs() < 2.0 {
            let _ = self.stop_all();
            return true;
        }

        // 旋转以纠正偏差
        // 正偏差意味着我们需要逆时针旋转来纠正
        if yaw_deviation > 0.0 {
            let _ = self.rotate_counter_clockwise(speed);
        } else {
            let _ = self.rotate_clockwise(speed);
        }

        false // 尚未纠正
    }

    /// 获取左前轮电机的可变引用
    pub fn fl_motor(&mut self) -> &mut FL {
        &mut self.fl_motor
    }

    /// 获取右前轮电机的可变引用
    pub fn fr_motor(&mut self) -> &mut FR {
        &mut self.fr_motor
    }

    /// 获取左后轮电机的可变引用
    pub fn bl_motor(&mut self) -> &mut BL {
        &mut self.bl_motor
    }

    /// 获取右后轮电机的可变引用
    pub fn br_motor(&mut self) -> &mut BR {
        &mut self.br_motor
    }
}
